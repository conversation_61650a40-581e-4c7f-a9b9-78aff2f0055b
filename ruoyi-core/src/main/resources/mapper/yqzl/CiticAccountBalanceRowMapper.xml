<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.yqzl.mapper.CiticAccountBalanceRowMapper">

    <resultMap type="CiticAccountBalanceRow" id="CiticAccountBalanceRowResult">
        <result property="id"    column="id"    />
        <result property="status"    column="status"    />
        <result property="statusText"    column="status_text"    />
        <result property="accountNo"    column="account_no"    />
        <result property="accountName"    column="account_name"    />
        <result property="currencyId"    column="currency_id"    />
        <result property="openBankName"    column="open_bank_name"    />
        <result property="lastTranDate"    column="last_tran_date"    />
        <result property="usableBalance"    column="usable_balance"    />
        <result property="balance"    column="balance"    />
        <result property="forzenAmt"    column="forzen_amt"    />
        <result property="frozenFlag"    column="frozen_flag"    />
        <result property="accountType"    column="account_type"    />
        <result property="lawptLmt"    column="lawpt_lmt"    />
        <result property="queryDate"    column="query_date"    />
    </resultMap>

    <sql id="selectCiticAccountBalanceRowVo">
        select id, status, status_text, account_no, account_name, currency_id, open_bank_name, last_tran_date, usable_balance, balance, forzen_amt, frozen_flag, account_type, lawpt_lmt, query_date from yqzl_citic_account_balance_row
    </sql>

    <select id="selectCiticAccountBalanceRowList" parameterType="CiticAccountBalanceRowVo" resultMap="CiticAccountBalanceRowResult">
        <include refid="selectCiticAccountBalanceRowVo"/>
        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="statusText != null  and statusText != ''"> and status_text = #{statusText}</if>
            <if test="accountNo != null  and accountNo != ''"> and account_no like concat('%', #{accountNo} , '%')</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="currencyId != null  and currencyId != ''"> and currency_id = #{currencyId}</if>
            <if test="openBankName != null  and openBankName != ''"> and open_bank_name like concat('%', #{openBankName}, '%')</if>
            <if test="lastTranDate != null  and lastTranDate != ''"> and last_tran_date = #{lastTranDate}</if>
            <if test="usableBalance != null "> and usable_balance = #{usableBalance}</if>
            <if test="balance != null "> and balance = #{balance}</if>
            <if test="forzenAmt != null "> and forzen_amt = #{forzenAmt}</if>
            <if test="frozenFlag != null  and frozenFlag != ''"> and frozen_flag = #{frozenFlag}</if>
            <if test="accountType != null  and accountType != ''"> and account_type = #{accountType}</if>
            <if test="lawptLmt != null "> and lawpt_lmt = #{lawptLmt}</if>
            <if test="queryDate != null "> and query_date = #{queryDate}</if>
            <if test="startTime != null"><!-- 开始时间检索 -->
                and date_format(query_date,'%Y-%m-%d') &gt;= date_format(#{startTime},'%Y-%m-%d')
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                and date_format(query_date,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
            </if>
        </where>
        order by query_date
        <if test="queryDateSort != null and queryDateSort == 'asc'">asc</if>
        <if test="queryDateSort == null or queryDateSort != 'asc'">desc</if>
    </select>

    <select id="selectCiticAccountBalanceRowById" parameterType="Long" resultMap="CiticAccountBalanceRowResult">
        <include refid="selectCiticAccountBalanceRowVo"/>
        where id = #{id}
    </select>

    <insert id="insertCiticAccountBalanceRow" parameterType="CiticAccountBalanceRow" useGeneratedKeys="true" keyProperty="id">
        insert into yqzl_citic_account_balance_row
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="status != null">status,</if>
            <if test="statusText != null">status_text,</if>
            <if test="accountNo != null and accountNo != ''">account_no,</if>
            <if test="accountName != null">account_name,</if>
            <if test="currencyId != null">currency_id,</if>
            <if test="openBankName != null">open_bank_name,</if>
            <if test="lastTranDate != null">last_tran_date,</if>
            <if test="usableBalance != null">usable_balance,</if>
            <if test="balance != null">balance,</if>
            <if test="forzenAmt != null">forzen_amt,</if>
            <if test="frozenFlag != null">frozen_flag,</if>
            <if test="accountType != null">account_type,</if>
            <if test="lawptLmt != null">lawpt_lmt,</if>
            <if test="queryDate != null">query_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="status != null">#{status},</if>
            <if test="statusText != null">#{statusText},</if>
            <if test="accountNo != null and accountNo != ''">#{accountNo},</if>
            <if test="accountName != null">#{accountName},</if>
            <if test="currencyId != null">#{currencyId},</if>
            <if test="openBankName != null">#{openBankName},</if>
            <if test="lastTranDate != null">#{lastTranDate},</if>
            <if test="usableBalance != null">#{usableBalance},</if>
            <if test="balance != null">#{balance},</if>
            <if test="forzenAmt != null">#{forzenAmt},</if>
            <if test="frozenFlag != null">#{frozenFlag},</if>
            <if test="accountType != null">#{accountType},</if>
            <if test="lawptLmt != null">#{lawptLmt},</if>
            <if test="queryDate != null">#{queryDate},</if>
         </trim>
    </insert>

    <update id="updateCiticAccountBalanceRow" parameterType="CiticAccountBalanceRow">
        update yqzl_citic_account_balance_row
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="statusText != null">status_text = #{statusText},</if>
            <if test="accountNo != null and accountNo != ''">account_no = #{accountNo},</if>
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="currencyId != null">currency_id = #{currencyId},</if>
            <if test="openBankName != null">open_bank_name = #{openBankName},</if>
            <if test="lastTranDate != null">last_tran_date = #{lastTranDate},</if>
            <if test="usableBalance != null">usable_balance = #{usableBalance},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="forzenAmt != null">forzen_amt = #{forzenAmt},</if>
            <if test="frozenFlag != null">frozen_flag = #{frozenFlag},</if>
            <if test="accountType != null">account_type = #{accountType},</if>
            <if test="lawptLmt != null">lawpt_lmt = #{lawptLmt},</if>
            <if test="queryDate != null">query_date = #{queryDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCiticAccountBalanceRowById" parameterType="Long">
        delete from yqzl_citic_account_balance_row where id = #{id}
    </delete>

    <delete id="deleteCiticAccountBalanceRowByIds" parameterType="String">
        delete from yqzl_citic_account_balance_row where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertCiticAccountBalanceRow" parameterType="java.util.List">
        INSERT INTO yqzl_citic_account_balance_row
        (
            status, status_text, account_no, account_name,
            currency_id, open_bank_name, last_tran_date, usable_balance,
            balance, forzen_amt, frozen_flag, account_type,
            lawpt_lmt, query_date
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.status},
            #{item.statusText},
            #{item.accountNo},
            #{item.accountName},
            #{item.currencyId},
            #{item.openBankName},
            #{item.lastTranDate},
            #{item.usableBalance},
            #{item.balance},
            #{item.forzenAmt},
            #{item.frozenFlag},
            #{item.accountType},
            #{item.lawptLmt},
            now()
        )
        </foreach>
    </insert>
</mapper>

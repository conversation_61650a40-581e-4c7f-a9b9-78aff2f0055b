package org.ruoyi.core.yqzl.domain;

import java.math.BigDecimal;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 中信银行账户明细信息查询对象 yqzl_citic_account_transaction
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
public class CiticAccountTransaction extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 交易日期 YYYYMMDD */
    @Excel(name = "交易日期 YYYYMMDD")
    private String tranDate;

    /** 交易时间 HHMMSS */
    @Excel(name = "交易时间 HHMMSS")
    private String tranTime;

    /** 柜员交易号 */
    @Excel(name = "柜员交易号")
    private String tranNo;

    /** 总交易流水号 */
    @Excel(name = "总交易流水号")
    private String sumTranNo;

    /** 交易金额 */
    @Excel(name = "交易金额")
    private BigDecimal tranAmount;

    /** 借贷标识 D借 C贷 */
    @Excel(name = "借贷标识 D借 C贷")
    private String creditDebitFlag;

    /** 对方账号 */
    @Excel(name = "对方账号")
    private String oppAccountNo;

    /** 对方账户名称 */
    @Excel(name = "对方账户名称")
    private String oppAccountName;

    /** 对方开户行名 */
    @Excel(name = "对方开户行名")
    private String oppOpenBankName;

    /** 附言 */
    @Excel(name = "附言")
    private String abstractField;

    /** 现转标识 0现金 1转账 */
    @Excel(name = "现转标识 0现金 1转账")
    private String cashTransferFlag;

    /** 网银制单员 */
    @Excel(name = "网银制单员")
    private String opId;

    /** 制单员姓名 */
    @Excel(name = "制单员姓名")
    private String opName;

    /** 网银审核员 */
    @Excel(name = "网银审核员")
    private String ckId;

    /** 审核员姓名 */
    @Excel(name = "审核员姓名")
    private String ckName;

    /** 账户余额 */
    @Excel(name = "账户余额")
    private BigDecimal balance;

    /** 起息日期 YYYYMMDD */
    @Excel(name = "起息日期 YYYYMMDD")
    private String valueDate;

    /** 主机交易码 */
    @Excel(name = "主机交易码")
    private String hostTranCode;

    /** 退汇日期 YYYYMMDD */
    @Excel(name = "退汇日期 YYYYMMDD")
    private String e3rtDate;

    /** 退汇标志 0退汇 1非退汇 */
    @Excel(name = "退汇标志 0退汇 1非退汇")
    private String e3rtFlag;

    /** 付款原有金额（信银国际） */
    @Excel(name = "付款原有金额", readConverterExp = "信=银国际")
    private BigDecimal oriDebitAmt;

    /** 付款原有币种（信银国际） */
    @Excel(name = "付款原有币种", readConverterExp = "信=银国际")
    private String oriDebitCry;

    /** 收款原有金额（信银国际） */
    @Excel(name = "收款原有金额", readConverterExp = "信=银国际")
    private BigDecimal oriCreditAmt;

    /** 收款原有币种（信银国际） */
    @Excel(name = "收款原有币种", readConverterExp = "信=银国际")
    private String oriCreditCry;

    /** 交易币种（信银国际） */
    @Excel(name = "交易币种", readConverterExp = "信=银国际")
    private String traCryType;

    /** 信银国际交易参考号 */
    @Excel(name = "信银国际交易参考号")
    private String tranRefNo;

    /** 客户流水号（信银国际） */
    @Excel(name = "客户流水号", readConverterExp = "信=银国际")
    private String clientId;

    /** 对账编号 */
    @Excel(name = "对账编号")
    private String chkNum;

    /** 关联交易日志号 */
    @Excel(name = "关联交易日志号")
    private String rlTranNo;

    /** 冲账对方交易日期 YYYYMMDD */
    @Excel(name = "冲账对方交易日期 YYYYMMDD")
    private String rfTranDt;

    /** 冲账对方柜员交易号 */
    @Excel(name = "冲账对方柜员交易号")
    private String rfTranNo;

    /** 附属账户 */
    @Excel(name = "附属账户")
    private String subAcccNo;

    /** 摘要内容 */
    @Excel(name = "摘要内容")
    private String hostTranDesc;

    /** 原始流水号 */
    @Excel(name = "原始流水号")
    private String oriNum;

    /**
     * 交易账号
     */
    private String accountNo;

    /**
     * 交易账号开户网点名称
     */
    private String openBankName;
}

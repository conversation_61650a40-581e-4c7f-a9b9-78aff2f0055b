package org.ruoyi.core.yqzl.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 中信银行账户余额查询结果对象 yqzl_citic_account_balance_row
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class CiticAccountBalanceRow extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 账户状态 */
    @Excel(name = "账户状态")
    private String status;

    /** 账户状态信息 */
    @Excel(name = "账户状态信息")
    private String statusText;

    /** 账号 */
    @Excel(name = "账号")
    private String accountNo;

    /** 账户名称 */
    @Excel(name = "账户名称")
    private String accountName;

    /** 币种 */
    @Excel(name = "币种")
    private String currencyId;

    /** 开户行名称 */
    @Excel(name = "开户行名称")
    private String openBankName;

    /** 最近交易日 YYYYMMDD */
    @Excel(name = "最近交易日 YYYYMMDD")
    private String lastTranDate;

    /** 可用账户余额 */
    @Excel(name = "可用账户余额")
    private BigDecimal usableBalance;

    /** 账号余额 */
    @Excel(name = "账号余额")
    private BigDecimal balance;

    /** 冻结（或看管）金额 */
    @Excel(name = "冻结", readConverterExp = "或=看管")
    private BigDecimal forzenAmt;

    /** 账号状态 A:正常 D:睡眠 F:冻结 */
    @Excel(name = "账号状态 A:正常 D:睡眠 F:冻结")
    private String frozenFlag;

    /** 账户类型 ST:活期储蓄 IM:活期支票 */
    @Excel(name = "账户类型 ST:活期储蓄 IM:活期支票")
    private String accountType;

    /** 法透额度 */
    @Excel(name = "法透额度")
    private BigDecimal lawptLmt;

    /** 查询日期 YYYY-MM-DD */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "查询日期 YYYY-MM-DD", width = 30, dateFormat = "yyyy-MM-dd")
    private Date queryDate;
}

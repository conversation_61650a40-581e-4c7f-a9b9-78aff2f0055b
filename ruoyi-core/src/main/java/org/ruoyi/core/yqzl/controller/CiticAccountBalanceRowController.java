package org.ruoyi.core.yqzl.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.yqzl.domain.CiticAccountBalanceRow;
import org.ruoyi.core.yqzl.domain.vo.CiticAccountBalanceRowVo;
import org.ruoyi.core.yqzl.service.ICiticAccountBalanceRowService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 中信银行账户余额查询结果Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/account/balance/row")
public class CiticAccountBalanceRowController extends BaseController
{
    @Autowired
    private ICiticAccountBalanceRowService citicAccountBalanceRowService;

    /**
     * 查询中信银行账户余额查询结果列表
     */
    //@PreAuthorize("@ss.hasPermi('system:row:list')")
    @GetMapping("/list")
    public TableDataInfo list(CiticAccountBalanceRowVo citicAccountBalanceRow)
    {
        startPage();
        List<CiticAccountBalanceRow> list = citicAccountBalanceRowService.selectCiticAccountBalanceRowList(citicAccountBalanceRow);
        return getDataTable(list);
    }

    /**
     * 导出中信银行账户余额查询结果列表
     */
    //@PreAuthorize("@ss.hasPermi('system:row:export')")
    @Log(title = "中信银行账户余额查询结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CiticAccountBalanceRowVo citicAccountBalanceRow)
    {
        List<CiticAccountBalanceRow> list = citicAccountBalanceRowService.selectCiticAccountBalanceRowList(citicAccountBalanceRow);
        ExcelUtil<CiticAccountBalanceRow> util = new ExcelUtil<CiticAccountBalanceRow>(CiticAccountBalanceRow.class);
        util.exportExcel(response, list, "中信银行账户余额查询结果数据");
    }

    /**
     * 获取中信银行账户余额查询结果详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:row:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(citicAccountBalanceRowService.selectCiticAccountBalanceRowById(id));
    }

    /**
     * 新增中信银行账户余额查询结果
     */
    //@PreAuthorize("@ss.hasPermi('system:row:add')")
    @Log(title = "中信银行账户余额查询结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CiticAccountBalanceRow citicAccountBalanceRow)
    {
        return toAjax(citicAccountBalanceRowService.insertCiticAccountBalanceRow(citicAccountBalanceRow));
    }

    /**
     * 修改中信银行账户余额查询结果
     */
    //@PreAuthorize("@ss.hasPermi('system:row:edit')")
    @Log(title = "中信银行账户余额查询结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CiticAccountBalanceRow citicAccountBalanceRow)
    {
        return toAjax(citicAccountBalanceRowService.updateCiticAccountBalanceRow(citicAccountBalanceRow));
    }

    /**
     * 删除中信银行账户余额查询结果
     */
    //@PreAuthorize("@ss.hasPermi('system:row:remove')")
    @Log(title = "中信银行账户余额查询结果", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(citicAccountBalanceRowService.deleteCiticAccountBalanceRowByIds(ids));
    }
}

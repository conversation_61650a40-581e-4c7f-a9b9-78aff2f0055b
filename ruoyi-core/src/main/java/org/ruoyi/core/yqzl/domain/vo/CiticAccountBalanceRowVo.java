package org.ruoyi.core.yqzl.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.ruoyi.core.yqzl.domain.CiticAccountBalanceRow;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 中信银行账户余额查询结果对象 yqzl_citic_account_balance_row
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class CiticAccountBalanceRowVo extends CiticAccountBalanceRow
{
    /**
     * 排序方式 asc正序 desc倒序
     */
    private String queryDateSort;

    /**
     * 搜索条件 起止时间-开始
     */
    private java.sql.Date startTime;

    /**
     * 搜索条件 起止时间-结束
     */
    private Date endTime;
}
